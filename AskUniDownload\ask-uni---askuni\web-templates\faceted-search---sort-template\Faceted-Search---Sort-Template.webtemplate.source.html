{%raw%}
<script id="search-order-select" type="text/x-handlebars-template">
  {{#if sortingOptions}}
  <select id="search_sort_options" tabIndex="0" aria-readonly="true">
    {{#each sortingOptions}}
      {{#ifvalue this value="relevance" }}
        <option value="{{this}}">{%endraw%} {% editable snippets "Search/Facet/SortOrder/Relevance" default: resx.Sorting_Relevance %}{%raw%}</option>
      {{/ifvalue}}
      {{#ifvalue this value="rating" }}
        <option value="{{this}}">{%endraw%} {% editable snippets "Search/Facet/SortOrder/AverageUserRating" default: resx.Sorting_Rating %}{%raw%}</option>
      {{/ifvalue}}
      {{#ifvalue this value="knowledgearticleviews" }}
        <option value="{{this}}">{%endraw%} {% editable snippets "Search/Facet/SortOrder/Views" default: resx.Sorting_ViewCount %}{%raw%}</option>
      {{/ifvalue}}
    {{/each}}
  </select>
  <label for="search_sort_options" class="visually-hidden">{%endraw%}{{resx.Sorting_Options}}{%raw%}</label>
  {{/if}}
</script>
{% endraw %}