<div class="row sectionBlockLayout" data-component-theme="portalThemeColor1" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h1 style="text-align: center; color: #2c3e50;">🎓 AskUni Student Portal Guide</h1>
            <p style="text-align: center; font-size: 18px; color: #7f8c8d;">Your digital gateway to academic excellence and campus life</p>
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;">
                <h3 style="margin: 0; color: white;">🚀 New to the portal? Start here!</h3>
                <p style="margin: 10px 0; color: white;">Follow our step-by-step guide to master your student portal in minutes</p>
            </div>
        </div>
    </div>
</div>

<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2 style="color: #3498db; text-align: center;">🔐 Getting Started: Your First Login</h2>
            <div style="background-color: #ecf0f1; padding: 20px; border-radius: 8px; margin: 10px 0;">
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                    <div style="background-color: #3498db; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">1</div>
                    <div>
                        <strong>Access the Portal:</strong> Visit <code>portal.askuni.edu</code> or click the "Student Login" button on our homepage
                    </div>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                    <div style="background-color: #e74c3c; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">2</div>
                    <div>
                        <strong>Login Credentials:</strong> Use your Student ID as username and your birthdate (MMDDYYYY) as initial password
                    </div>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                    <div style="background-color: #f39c12; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">3</div>
                    <div>
                        <strong>Security Setup:</strong> You'll be prompted to change your password and set up two-factor authentication
                    </div>
                </div>
                <div style="display: flex; align-items: center;">
                    <div style="background-color: #27ae60; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">4</div>
                    <div>
                        <strong>Profile Completion:</strong> Update your contact information, emergency contacts, and profile picture
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row sectionBlockLayout" data-component-theme="portalThemeColor3" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2 style="color: #9b59b6;">📱 Portal Navigation Made Easy</h2>
            <div style="background-color: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h4 style="color: #3498db; margin-top: 0;">🏠 Dashboard Overview</h4>
                <p>Your homepage displays: upcoming assignments, class schedule, recent grades, and important announcements. Customize widgets by clicking the "⚙️ Settings" icon.</p>
            </div>
            <div style="background-color: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h4 style="color: #e74c3c; margin-top: 0;">📚 Academic Hub</h4>
                <p>Access courses, view syllabi, submit assignments, check grades, and communicate with professors. Use the search bar to quickly find specific courses or materials.</p>
            </div>
        </div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2 style="color: #f39c12;">⚡ Quick Actions Toolbar</h2>
            <div style="background-color: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h4 style="color: #27ae60; margin-top: 0;">🔍 Smart Search</h4>
                <p>Type "/" anywhere in the portal to open quick search. Find courses, professors, assignments, or campus resources instantly.</p>
            </div>
            <div style="background-color: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h4 style="color: #9b59b6; margin-top: 0;">🔔 Notification Center</h4>
                <p>Click the bell icon to view all notifications. Set preferences for email, SMS, or push notifications for different types of updates.</p>
            </div>
        </div>
    </div>
</div>

<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2 style="text-align: center; color: #2c3e50;">🎯 Essential Portal Features & How to Use Them</h2>
        </div>
    </div>
</div>

<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <div style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h3 style="margin-top: 0; color: white;">📝 Course Registration</h3>
                <p style="color: white; margin-bottom: 15px;"><strong>Step-by-step process:</strong></p>
                <ol style="color: white; line-height: 1.6;">
                    <li>Navigate to "Academic" → "Course Registration"</li>
                    <li>Select your term (Fall 2024, Spring 2025, etc.)</li>
                    <li>Use the course search filters: subject, time, instructor</li>
                    <li>Click "Add to Cart" for desired courses</li>
                    <li>Check for conflicts in your schedule preview</li>
                    <li>Submit registration and print confirmation</li>
                </ol>
                <p style="color: white; font-size: 14px;"><strong>💡 Pro Tip:</strong> Use the "Waitlist" feature for full courses!</p>
            </div>
        </div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <div style="background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h3 style="margin-top: 0; color: white;">📊 Grade Tracking</h3>
                <p style="color: white; margin-bottom: 15px;"><strong>Monitor your progress:</strong></p>
                <ol style="color: white; line-height: 1.6;">
                    <li>Go to "Academic" → "Grades & Transcripts"</li>
                    <li>Select current or past terms</li>
                    <li>View detailed grade breakdowns per course</li>
                    <li>Track GPA calculations in real-time</li>
                    <li>Set up grade alerts for important assignments</li>
                    <li>Download unofficial transcripts anytime</li>
                </ol>
                <p style="color: white; font-size: 14px;"><strong>💡 Pro Tip:</strong> Enable email notifications for new grades!</p>
            </div>
        </div>
    </div>
</div>

<div class="row sectionBlockLayout" data-component-theme="portalThemeColor5" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <div style="background: linear-gradient(135deg, #00b894 0%, #00a085 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h3 style="margin-top: 0; color: white;">💰 Financial Management</h3>
                <p style="color: white; margin-bottom: 15px;"><strong>Handle your finances:</strong></p>
                <ul style="color: white; line-height: 1.6;">
                    <li><strong>View Account:</strong> "Student Finances" → "Account Summary"</li>
                    <li><strong>Make Payments:</strong> Online payment with credit/debit cards</li>
                    <li><strong>Payment Plans:</strong> Set up monthly installment plans</li>
                    <li><strong>Financial Aid:</strong> Track aid status and requirements</li>
                    <li><strong>1098-T Forms:</strong> Download tax documents</li>
                </ul>
                <p style="color: white; font-size: 14px;"><strong>⚠️ Important:</strong> Set up autopay to avoid late fees!</p>
            </div>
        </div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <div style="background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h3 style="margin-top: 0; color: white;">📚 Assignment Submission</h3>
                <p style="color: white; margin-bottom: 15px;"><strong>Submit work efficiently:</strong></p>
                <ul style="color: white; line-height: 1.6;">
                    <li><strong>Access:</strong> "Courses" → Select course → "Assignments"</li>
                    <li><strong>Upload:</strong> Drag & drop files or use browse button</li>
                    <li><strong>Formats:</strong> PDF, DOC, PPT, images (max 50MB)</li>
                    <li><strong>Drafts:</strong> Save drafts before final submission</li>
                    <li><strong>Confirmation:</strong> Always check submission receipt</li>
                </ul>
                <p style="color: white; font-size: 14px;"><strong>⏰ Reminder:</strong> Portal shows time remaining until deadline!</p>
            </div>
        </div>
    </div>
</div>

<div class="row sectionBlockLayout" data-component-theme="portalThemeColor3" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2 style="text-align: center; color: #2c3e50;">🔧 Portal Customization & Advanced Features</h2>
        </div>
    </div>
</div>

<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-4 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <div style="background-color: #f8f9fa; border-left: 4px solid #9b59b6; padding: 20px; margin-bottom: 20px;">
                <h3 style="color: #9b59b6; margin-top: 0;">🎨 Personalize Your Dashboard</h3>
                <ul style="line-height: 1.6;">
                    <li><strong>Widget Management:</strong> Drag & drop to rearrange dashboard widgets</li>
                    <li><strong>Theme Selection:</strong> Choose from 5 color themes in Settings</li>
                    <li><strong>Quick Links:</strong> Add frequently used pages to your sidebar</li>
                    <li><strong>Notification Preferences:</strong> Customize what alerts you receive</li>
                    <li><strong>Calendar Integration:</strong> Sync with Google/Outlook calendars</li>
                </ul>
                <p style="font-size: 14px; color: #6c757d;"><strong>Access:</strong> Click your profile picture → "Customize Dashboard"</p>
            </div>
        </div>
        <div class="col-lg-4 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <div style="background-color: #f8f9fa; border-left: 4px solid #f39c12; padding: 20px; margin-bottom: 20px;">
                <h3 style="color: #f39c12; margin-top: 0;">📱 Mobile App Features</h3>
                <ul style="line-height: 1.6;">
                    <li><strong>Download:</strong> "AskUni Mobile" from App Store/Google Play</li>
                    <li><strong>Offline Access:</strong> View downloaded course materials offline</li>
                    <li><strong>Push Notifications:</strong> Real-time alerts for grades & announcements</li>
                    <li><strong>QR Scanner:</strong> Scan codes for quick attendance check-in</li>
                    <li><strong>Campus Map:</strong> GPS navigation to classrooms & facilities</li>
                </ul>
                <p style="font-size: 14px; color: #6c757d;"><strong>Login:</strong> Use same credentials as web portal</p>
            </div>
        </div>
        <div class="col-lg-4 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <div style="background-color: #f8f9fa; border-left: 4px solid #27ae60; padding: 20px; margin-bottom: 20px;">
                <h3 style="color: #27ae60; margin-top: 0;">🆘 Getting Help</h3>
                <ul style="line-height: 1.6;">
                    <li><strong>Live Chat:</strong> Click the chat bubble (bottom right) for instant help</li>
                    <li><strong>Video Tutorials:</strong> "Help" → "Tutorial Library" for step-by-step guides</li>
                    <li><strong>FAQ Section:</strong> Search common questions and solutions</li>
                    <li><strong>Submit Ticket:</strong> Report technical issues through Help Desk</li>
                    <li><strong>Phone Support:</strong> (555) 123-HELP (4357) - 24/7 availability</li>
                </ul>
                <p style="font-size: 14px; color: #6c757d;"><strong>Emergency:</strong> Use "Report Issue" for urgent problems</p>
            </div>
        </div>
    </div>
</div>

<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2 style="color: #e74c3c;">🚨 Common Portal Issues & Quick Fixes</h2>
            <div style="background-color: #fff5f5; border: 1px solid #fed7d7; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h4 style="color: #e53e3e; margin-top: 0;">🔒 Can't Login?</h4>
                <ul style="margin-bottom: 0;">
                    <li>Clear browser cache and cookies</li>
                    <li>Try incognito/private browsing mode</li>
                    <li>Reset password using "Forgot Password" link</li>
                    <li>Contact IT if account is locked</li>
                </ul>
            </div>
            <div style="background-color: #fffbf0; border: 1px solid #fbd38d; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h4 style="color: #d69e2e; margin-top: 0;">📱 Mobile App Not Working?</h4>
                <ul style="margin-bottom: 0;">
                    <li>Update app to latest version</li>
                    <li>Check internet connection</li>
                    <li>Force close and restart app</li>
                    <li>Re-login with fresh credentials</li>
                </ul>
            </div>
            <div style="background-color: #f0fff4; border: 1px solid #9ae6b4; padding: 15px; border-radius: 8px;">
                <h4 style="color: #38a169; margin-top: 0;">📊 Grades Not Showing?</h4>
                <ul style="margin-bottom: 0;">
                    <li>Refresh page (Ctrl+F5 or Cmd+R)</li>
                    <li>Check if grades are released by instructor</li>
                    <li>Verify you're viewing correct term</li>
                    <li>Contact professor if grades are missing</li>
                </ul>
            </div>
        </div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2 style="color: #3498db;">💡 Pro Tips for Portal Mastery</h2>
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 15px;">
                <h4 style="color: white; margin-top: 0;">⚡ Keyboard Shortcuts</h4>
                <ul style="color: white; margin-bottom: 0;">
                    <li><strong>Ctrl + /</strong> - Open quick search</li>
                    <li><strong>Ctrl + D</strong> - Go to dashboard</li>
                    <li><strong>Ctrl + G</strong> - View grades</li>
                    <li><strong>Ctrl + C</strong> - Open courses</li>
                    <li><strong>Ctrl + M</strong> - Check messages</li>
                </ul>
            </div>
            <div style="background-color: #f7fafc; border: 1px solid #cbd5e0; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h4 style="color: #2d3748; margin-top: 0;">📅 Calendar Integration</h4>
                <p style="margin-bottom: 10px;">Sync your academic calendar:</p>
                <ol style="margin-bottom: 0;">
                    <li>Go to "Calendar" → "Settings"</li>
                    <li>Click "Export Calendar"</li>
                    <li>Copy the iCal URL</li>
                    <li>Add to Google Calendar or Outlook</li>
                </ol>
            </div>
            <div style="background-color: #edf2f7; border: 1px solid #a0aec0; padding: 15px; border-radius: 8px;">
                <h4 style="color: #2d3748; margin-top: 0;">🔔 Smart Notifications</h4>
                <p style="margin-bottom: 10px;">Optimize your alerts:</p>
                <ul style="margin-bottom: 0;">
                    <li>Set assignment reminders 24-48 hours early</li>
                    <li>Enable grade notifications for important courses</li>
                    <li>Turn off non-essential notifications during exams</li>
                    <li>Use "Do Not Disturb" mode during study hours</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row sectionBlockLayout" data-component-theme="portalThemeColor1" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2 style="text-align: center; color: #2c3e50;">🎓 Ready to Excel? Your Next Steps</h2>
            <div style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 30px; border-radius: 15px; margin: 20px 0; text-align: center;">
                <h3 style="color: white; margin-top: 0;">🚀 Start Your Portal Journey Today!</h3>
                <div style="display: flex; justify-content: space-around; flex-wrap: wrap; margin-top: 20px;">
                    <div style="background-color: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 5px; min-width: 200px;">
                        <h4 style="color: white; margin: 0;">Step 1</h4>
                        <p style="color: white; margin: 5px 0;">Login & Complete Profile</p>
                    </div>
                    <div style="background-color: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 5px; min-width: 200px;">
                        <h4 style="color: white; margin: 0;">Step 2</h4>
                        <p style="color: white; margin: 5px 0;">Explore Dashboard Features</p>
                    </div>
                    <div style="background-color: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 5px; min-width: 200px;">
                        <h4 style="color: white; margin: 0;">Step 3</h4>
                        <p style="color: white; margin: 5px 0;">Download Mobile App</p>
                    </div>
                    <div style="background-color: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 5px; min-width: 200px;">
                        <h4 style="color: white; margin: 0;">Step 4</h4>
                        <p style="color: white; margin: 5px 0;">Master Your Courses</p>
                    </div>
                </div>
                <p style="color: white; margin-top: 20px; font-size: 18px;">Need help? Our support team is here 24/7 to ensure your success! 🌟</p>
            </div>
        </div>
    </div>
</div>
