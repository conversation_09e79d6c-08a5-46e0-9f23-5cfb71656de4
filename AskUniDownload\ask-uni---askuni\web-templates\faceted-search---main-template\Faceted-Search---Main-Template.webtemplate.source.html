{% assign search_page = sitemarkers['Search'] %}
{% assign query = settings['search/query'] | default:'@(Query)' %}
{% block main %}
<div class="handlebars-search-container" data-url="{{ search_page.url | h }}" data-query="{{query}}">
 <div class="row search-body-container">
   <div class="col-sm-12 loader"><div class="fa-spin"><span class="fa fa-spinner fa-4x" aria-hidden="true"></span></div></div>
 </div>
 
  <div class="js-facet-order-definition d-none">
   <div class="facet-order-item">_logicalname</div>
   <div class="facet-order-item">modifiedon.date</div>
   <div class="facet-order-item">rating</div>
   <div class="facet-order-item">associated.product</div>
  </div>
  
  {% include 'Faceted Search - Paging Template' %}
  {% include 'Faceted Search - Sort Template' %}  
  {% include 'Faceted Search - Facets Template' %}
  {% include 'Faceted Search - Results Template' %}  
</div>

{%raw%}
<script id="facets-view-body-container" type="text/x-handlebars-template">
 {{#if facetViews }}
  <div class="col-lg-3 col-md-4 facets"></div>  
  <div class="col-lg-9 col-md-8 col-sm-12 loader"><div class="fa-spin"><span class="fa fa-spinner fa-4x" aria-hidden="true"></span></div></div>
  <div class="col-lg-9 col-md-8 col-sm-12 js-search-body">
   <div class=" d-none d-sm-block d-md-block d-lg-block hidden-xs search-order js-search-body float-end"></div>
   <div class="search-results" role="alert"></div>
   <div class="search-pagination text-center"></div>
  </div>
  {{else}}
  <div class="col-lg-12 col-md-12 col-sm-12 js-search-body">
   <div class=" d-none d-sm-block d-md-block d-lg-block hidden-xs search-order js-search-body float-end"></div>
   <div class="search-results"></div>
   <div class="search-pagination text-center"></div>
  </div>  
  {{/if}}
</script>
{% endraw %}
{% endblock %}