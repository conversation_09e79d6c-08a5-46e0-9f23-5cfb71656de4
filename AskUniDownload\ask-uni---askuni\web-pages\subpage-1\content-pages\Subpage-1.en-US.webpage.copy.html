<div class="row sectionBlockLayout" data-component-theme="portalThemeColor6" style="display: flex; flex-wrap: wrap; height: 15px; min-height: 15px;">
</div>
<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2>Subpage one</h2>
            <p>This page can be customized by adding new sections and components.</p>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><img src="/Geometric-2.png" name="Geometric-2.png" alt="" style="max-width: 100%; width: 132%; height: 336px; margin-left: auto; margin-right: auto;"></div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2>Product or service one</h2>
            <p>This section could provide testimonials, links to training or documentation, or introduce other actions your audience may want to take.</p>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor2" style="display: flex; flex-wrap: wrap; min-height: 28px;"></div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor6" style="display: flex; flex-wrap: wrap; min-height: 52px;"></div>