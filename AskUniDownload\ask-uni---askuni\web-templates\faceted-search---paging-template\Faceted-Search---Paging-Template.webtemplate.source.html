{%raw%}
 <script id="facets-view-pagination" type="text/x-handlebars-template">
  {{#if needsPagination}}
   <ul class="pagination">
    {{#ifvalue pageNumber value=1}}
     <li class=" page-item disabled"><span class="aspNetDisabled" aria-label="{%endraw%}{{ resx.Pagination_First_Page }}{%raw%}">«</span></li>
     <li class=" page-item disabled"><span class="aspNetDisabled" aria-label="{%endraw%}{{ resx.Pagination_Previous_Page }}{%raw%}">‹</span></li>
    {{else}}
     <li><a class=" page-item  page-link js-go-to-1" aria-label="{%endraw%}{{ resx.Pagination_First_Page }}{%raw%}" href="">«</a></li>
     <li><a class=" page-item  page-link js-go-to-{{previousPage}}" aria-label="{%endraw%}{{ resx.Pagination_Previous_Page }}{%raw%}" href="">‹</a></li>
    {{/ifvalue}}
    {{#each pageLinks}}
     {{#ifvalue this value=../pageNumber}}
      <li class=" page-item active" aria-label="{{ stringFormat "
       {%endraw%}{{ resx.Pagination_Current_Page }}{%raw%}" this}}" tabIndex="0"><span>{{this}}</span></li>
     {{else}}
      <li><a class=" page-item  page-link js-go-to-{{this}}" aria-label="{{ stringFormat "
       {%endraw%}{{ resx.Pagination_Page }}{%raw%}" this}}" href="">{{this}}</a></li>
     {{/ifvalue}}
    {{/each}}

    {{#ifvalue pageNumber value=pageCount}}
     <li class=" page-item disabled"><span aria-label="{%endraw%}{{ resx.Pagination_Next_Page }}{%raw%}">›</span></li>
     <li class=" page-item disabled"><span aria-label="{%endraw%}{{ resx.Pagination_Last_Page }}{%raw%}">»</span></li>
    {{else}}
     <li><a class=" page-item  page-link js-go-to-{{nextPage}}" aria-label="{%endraw%}{{ resx.Pagination_Next_Page }}{%raw%}" href="">›</a></li>
     <li><a class=" page-item  page-link js-go-to-{{pageCount}}" aria-label="{%endraw%}{{ resx.Pagination_Last_Page }}{%raw%}" href="">»</a></li>
    {{/ifvalue}}
   </ul>
  {{/if}}
 </script>
 {% endraw %}