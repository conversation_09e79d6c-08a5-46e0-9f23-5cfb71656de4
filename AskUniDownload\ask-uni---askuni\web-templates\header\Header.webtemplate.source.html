{% assign defaultlang = settings['LanguageLocale/Code'] | default: 'en-us' %}
{% assign homeurl = website.adx_partialurl %}
<div class=' navbar-expand-xl navbar navbar-dark static-top' style="flex-wrap:wrap;" role='banner'>
  <div class='skip-to-content'>
    <a href='#mainContent'>{{ resx.Skip_To_Content | default: 'Skip to main content' }}</a>
  </div>
  <div class='container  custom-container'>
    <!--
      div class=" d-xs-block d-sm-none d-md-none d-lg-none  ">
        {% editable snippets 'Mobile Header' type: 'html' %}
      </div
    -->
    <div class=' d-xs-block d-sm-block d-md-block d-lg-block navbar-brand navbar-header'>
      {% editable snippets 'Mobile Header' type: 'html' %}
    </div>
    <button type='button' class='navbar-toggler collapsed custom-navbar-toggler' title='{{ snippets["Header/Toggle Navigation"] | default: resx['Toggle_Navigation'] | h }}' data-bs-toggle='collapse' data-bs-target='#navbar' aria-expanded='false' onclick='setHeight();'>
      <span class='visually-hidden'>{{ snippets['Header/Toggle Navigation'] | default: resx.Toggle_Navigation | h }}</span>
      <span class='navbar-toggler-icon'></span>
    </button>

    <div id='navbar' class='navbar-collapse collapse' style="width: min-content;">
      {% assign primary_nav = weblinks.Default %}
      {% if primary_nav %}
        <nav aria-label='{{ resx.Main_Navigation | default: "Main Navigation" }}' class='ms-auto menu-bar {% if primary_nav.editable %}xrm-entity xrm-editable-adx_weblinkset{% endif %}' data-weblinks-maxdepth='2'>
          <ul class='nav navbar-nav weblinks'>
            {% for link in primary_nav.weblinks %}
              {% unless forloop.first %}
                <li class=' nav-item divider-vertical' aria-hidden='true'></li>
              {% endunless %}
              {% if link.display_page_child_links %}
                {% if link.url != null %}
                  {% assign sublinks = sitemap[link.url].children %}
                {% endif %}
              {% else %}
                {% assign sublinks = link.weblinks %}
              {% endif %}
              <li class=' nav-item weblink {% if sublinks.size > 0 %} dropdown{% endif %}'>
                <a
                  aria-label='{{ link.name | escape }}'
                  {% if sublinks.size > 0 -%}
                    href='#' role='button' class='dropdown-toggle' data-bs-toggle='dropdown'
                  {%- else -%}
                    href='{{ link.url | escape }}' aria-roledescription='link'
                  {%- endif -%}
                  {%- if link.Open_In_New_Window %}
                    target='_blank'
                  {% endif -%}
                  {%- if link.nofollow %}
                    rel='nofollow'
                  {% endif -%}
                  {%- if link.tooltip %}
                    title='{{ link.tooltip | escape }}'
                  {% endif %}
                >
                  {%- if link.image -%}
                    {%- if link.image.url -%}
                      {%- if link.image.url.first == '.' -%}
                        <span class='{{ link.image.url | split:'.' | join }}' aria-hidden='true'></span>
                      {%- endif -%}
                    {%- else -%}
                      <img
                        src='{{ link.image.url | escape }}'
                        alt='{{ link.image.alternate_text | default:link.tooltip | escape }}'
                        {% if link.image.width %}
                          width='{{ link.image.width | escape }}'
                        {% endif %}
                        {% if link.image.height %}
                          height='{{ link.image.height | escape }}'
                        {% endif %}
                      >
                    {%- endif -%}
                  {%- endif -%}
                  {%- unless link.display_image_only -%}
                    {{ link.name | escape }}
                  {%- endunless -%}
                  {%- if sublinks.size > 0 -%}
                    <span class='caret'></span>
                  {%- endif -%}
                </a>
                {% if sublinks.size > 0 %}
                  <ul class='dropdown-menu dropdown-menu-end'>
                    {% if link.name %}
                      <li>
                        <a
                          aria-label='{{ link.name | escape }}'
                          class='dropdown-item'
                          aria-roledescription='link'
                          href='{{ link.url }}'
                          {% if link.Open_In_New_Window %}
                            target='_blank'
                          {% endif %}
                          {% if link.nofollow %}
                            rel='nofollow'
                          {% endif %}
                          {% if link.tooltip %}
                            title='{{ link.tooltip | escape }}'
                          {% endif %}
                        >
                          {{- link.name | escape -}}
                        </a>
                      </li>
                      <div class='dropdown-divider'></div>
                    {% endif %}
                    {% for sublink in sublinks %}
                      <li>
                        <a
                          class="dropdown-item"
                          aria-label='{{ sublink.name | default:sublink.title | escape }}'
                          aria-roledescription='link'
                          href='{{ sublink.url }}'
                          {% if sublink.Open_In_New_Window %}
                            target='_blank'
                          {% endif %}
                          {% if sublink.nofollow %}
                            rel='nofollow'
                          {% endif %}
                          {% if sublink.tooltip %}
                            title='{{ sublink.tooltip | escape }}'
                          {% endif %}
                        >
                          {{ sublink.name | default: sublink.title | escape }}
                        </a>
                      </li>
                    {% endfor %}
                  </ul>
                {% endif %}
              </li>
            {% endfor %}
            {% assign search_enabled = settings['Search/Enabled'] | boolean | default: true %}
            {% if search_enabled %}
              <li class=' nav-item divider-vertical' aria-hidden='true'></li>
              <li class=' nav-item dropdown'>
                <a id='search' class=' nav-link navbar-icon' href='#' data-bs-toggle='dropdown' role='button' aria-expanded='false' aria-label='{{ snippets["Header/Search/ToolTip"] | default:resx["Search_DefaultText"] | escape }}'>
                  <span class='glyphicon fa-solid fa-magnifying-glass'></span>
                </a>
                <div class='dropdown-menu dropdown-search dropdown-menu-end'>
                  {% include 'Search', search_id: 'q' %}
                </div>
              </li>
            {% endif %}
            <li class=' nav-item divider-vertical' aria-hidden='true'></li>
            {% if website.languages.size > 1 %}
              <li class=' nav-item dropdown'>
                <a class=' nav-link dropdown-toggle' aria-roledescription='link' href='#' data-bs-toggle='dropdown' aria-label='{{ website.selected_language.name | escape }}' aria-haspopup='true' aria-expanded='false' title='{{ website.selected_language.name | escape }}'>
                  <span class='drop_language'>{{ website.selected_language.name | escape }}</span>
                  <span class='caret'></span>
                </a>
                {% include 'Languages Dropdown' %}
              </li>
              <li class=' nav-item divider-vertical' aria-hidden='true'></li>
            {% endif %}
            {% if user %}
              <li class=' nav-item dropdown'>
                {% assign username = user.fullname | escape %}
                <a href='#' class=' nav-link dropdown-toggle' aria-roledescription='link' title='{{username | default: resx['Default_Profile_name'] }}' data-bs-toggle='dropdown' role='button' aria-expanded='false'>
                  <span class='username'>{{ username | default: resx.Default_Profile_name }}</span>
                  <span class='caret'></span>
                </a>
                <ul class='dropdown-menu dropdown-menu-end'>
                  {% assign show_profile_nav = settings['Header/ShowAllProfileNavigationLinks'] | boolean | default: true %}
                  {% if show_profile_nav %}
                    {% assign profile_nav = weblinks['Profile Navigation'] %}
                    {% if profile_nav %}
                      {% for link in profile_nav.weblinks %}
                        <li>
                          <a class="dropdown-item"  aria-label='{{ link.name | escape }}' aria-roledescription='link' href='{{ link.url | escape }}' title='{{ link.name | escape }}'>{{ link.name | escape }}</a>
                        </li>
                      {% endfor %}
                    {% endif %}
                  {% else %}
                    <li>
                      <a class="dropdown-item"  aria-label='{{ snippets["Profile Link Text"] | default:resx["Profile_Text"] | escape }}' aria-roledescription='link' href='{{ sitemarkers['Profile'].url | escape }}'>{{ snippets['Profile Link Text'] | default: resx.Profile_Text | escape }}</a
                    </li>
                  {% endif %}
                  <li class='dropdown-divider' role='separator' aria-hidden='true'></li>
                  <li>
                    <a class="dropdown-item"  aria-label='{{ snippets["links/logout"] | default:resx["Sign_Out"] | escape }}' aria-roledescription='link' href='{% if homeurl%}/{{ homeurl }}{% endif %}{{ website.sign_out_url_substitution }}' title='{{ snippets["links/logout"] | default:resx["Sign_Out"] | escape }}'>
                      {{ snippets['links/logout'] | default: resx.Sign_Out | escape }}
                    </a>
                  </li>
                </ul>
              </li>
            {% else %}
              <li class="nav-item">
                <a class="nav-link" aria-label='{{ snippets["links/login"] | default:resx["Sign_In"] | escape }}' aria-roledescription='link' href='{% if homeurl%}/{{ homeurl }}{% endif %}{{ website.sign_in_url_substitution }}'>
                  {{ snippets['links/login'] | default: resx.Sign_In | escape }}
                </a>
              </li>
            {% endif %}
          </ul>
          {% editable primary_nav %}
        </nav>
      {% endif %}
    </div>
  </div>
</div>
{% substitution %}
{% assign current_page = page.id %}
{% assign sr_page = sitemarkers.Search.id %}
{% assign forum_page = sitemarkers.Forums.id %}
{% if current_page %}
  {% if current_page == sr_page or current_page == forum_page %}
    {% assign section_class = 'section-landing-search' %}
    {% if current_page == forum_page %}
      {% assign section_class = 'section-landing-forums' %}
    {% endif %}
    <section class='page_section {{ section_class | h }} color-inverse'>
      <div class='row sectionBlockLayout sectionFixedStyle' style='display: flex; flex-wrap: wrap; text-align: center;'>
        <div class='container' style='display: flex; flex-wrap: wrap;'>
          <div class='col-lg-12 columnBlockLayout' style='display: flex; flex-direction: column; justify-content: center;'>
            {% if current_page == sr_page %}
              <h1 id='mainContent'>{% editable snippets 'Search/Title' default: resx["Discover_Contoso"] %}</h1>
              {% include 'Search', search_id: 'search_control' %}
            {% endif %}
          </div>
        </div>
      </div>
    </section>
  {% endif %}
{% endif %}
{% endsubstitution %}
<script type='text/javascript'>
  window.onload = function () {
    if (window.navigator.appName == 'Microsoft Internet Explorer' || window.navigator.userAgent.indexOf('Trident') > 0) {
      var searchElement = document.getElementById('search');
      if (searchElement != null) searchElement.setAttribute('href', '');
    }
  };
  function setHeight() {
    var windowHeight = window.innerHeight - 140;
    var navbar = document.getElementById('navbar');
    if (navbar) {
      navbar.style.maxHeight = windowHeight + 'px';
    }
  }
  window.addEventListener('resize', function (event) {
    setHeight();
  });
</script>
