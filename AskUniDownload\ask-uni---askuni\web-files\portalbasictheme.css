/**
 * Code generated by Microsoft. Changes to this file are not
 * supported. We recommend that you do not modify this file. Any
 * change to this file will get overwritten with a theme applied using
 * Site Styling panel from Power Apps Portals.
 */
:root {
  --portalThemeColor1: #2E456B;
  --portalThemeColor2: #FFCA4B;
  --portalThemeColor3: #277493;
  --portalThemeColor4: #FEA002;
  --portalThemeColor5: #F3DBB1;
  --portalThemeColor6: #88ABA2;
  --portalThemeColor7: #FFFFFF;
  --portalThemeColor8: #2A2C2E;
  --portalThemeColor9: #E58700;
  --portalThemeColor10: ;
  --portalThemeColor11: ;
  --portalThemeColor12: ;
  --portalThemeOnColor1: #FFFFFF;
  --portalThemeOnColor2: #2A2C2E;
  --portalThemeOnColor3: #FFFFFF;
  --portalThemeOnColor4: #2A2C2E;
  --portalThemeOnColor5: #2A2C2E;
  --portalThemeOnColor6: #2A2C2E;
  --portalThemeOnColor7: #2A2C2E;
  --portalThemeOnColor8: #FFFFFF;
  --portalThemeOnColor9: #2A2C2E;
  --portalThemeOnColor10: ;
  --portalThemeOnColor11: ;
  --portalThemeOnColor12: ; }

[data-component-theme="portalThemeColor1"] {
  background-color: var(--portalThemeColor1);
  color: var(--portalThemeOnColor1); }
  [data-component-theme="portalThemeColor1"] h1, [data-component-theme="portalThemeColor1"] h2, [data-component-theme="portalThemeColor1"] h3, [data-component-theme="portalThemeColor1"] h4, [data-component-theme="portalThemeColor1"] h5, [data-component-theme="portalThemeColor1"] h6, [data-component-theme="portalThemeColor1"] p, [data-component-theme="portalThemeColor1"] p.smallText {
    color: var(--portalThemeOnColor1); }

[data-component-theme="portalThemeColor2"] {
  background-color: var(--portalThemeColor2);
  color: var(--portalThemeOnColor2); }
  [data-component-theme="portalThemeColor2"] h1, [data-component-theme="portalThemeColor2"] h2, [data-component-theme="portalThemeColor2"] h3, [data-component-theme="portalThemeColor2"] h4, [data-component-theme="portalThemeColor2"] h5, [data-component-theme="portalThemeColor2"] h6, [data-component-theme="portalThemeColor2"] p, [data-component-theme="portalThemeColor2"] p.smallText {
    color: var(--portalThemeOnColor2); }

[data-component-theme="portalThemeColor3"] {
  background-color: var(--portalThemeColor3);
  color: var(--portalThemeOnColor3); }
  [data-component-theme="portalThemeColor3"] h1, [data-component-theme="portalThemeColor3"] h2, [data-component-theme="portalThemeColor3"] h3, [data-component-theme="portalThemeColor3"] h4, [data-component-theme="portalThemeColor3"] h5, [data-component-theme="portalThemeColor3"] h6, [data-component-theme="portalThemeColor3"] p, [data-component-theme="portalThemeColor3"] p.smallText {
    color: var(--portalThemeOnColor3); }

[data-component-theme="portalThemeColor4"] {
  background-color: var(--portalThemeColor4);
  color: var(--portalThemeOnColor4); }
  [data-component-theme="portalThemeColor4"] h1, [data-component-theme="portalThemeColor4"] h2, [data-component-theme="portalThemeColor4"] h3, [data-component-theme="portalThemeColor4"] h4, [data-component-theme="portalThemeColor4"] h5, [data-component-theme="portalThemeColor4"] h6, [data-component-theme="portalThemeColor4"] p, [data-component-theme="portalThemeColor4"] p.smallText {
    color: var(--portalThemeOnColor4); }

[data-component-theme="portalThemeColor5"] {
  background-color: var(--portalThemeColor5);
  color: var(--portalThemeOnColor5); }
  [data-component-theme="portalThemeColor5"] h1, [data-component-theme="portalThemeColor5"] h2, [data-component-theme="portalThemeColor5"] h3, [data-component-theme="portalThemeColor5"] h4, [data-component-theme="portalThemeColor5"] h5, [data-component-theme="portalThemeColor5"] h6, [data-component-theme="portalThemeColor5"] p, [data-component-theme="portalThemeColor5"] p.smallText {
    color: var(--portalThemeOnColor5); }

[data-component-theme="portalThemeColor6"] {
  background-color: var(--portalThemeColor6);
  color: var(--portalThemeOnColor6); }
  [data-component-theme="portalThemeColor6"] h1, [data-component-theme="portalThemeColor6"] h2, [data-component-theme="portalThemeColor6"] h3, [data-component-theme="portalThemeColor6"] h4, [data-component-theme="portalThemeColor6"] h5, [data-component-theme="portalThemeColor6"] h6, [data-component-theme="portalThemeColor6"] p, [data-component-theme="portalThemeColor6"] p.smallText {
    color: var(--portalThemeOnColor6); }

[data-component-theme="portalThemeColor7"] {
  background-color: var(--portalThemeColor7);
  color: var(--portalThemeOnColor7); }
  [data-component-theme="portalThemeColor7"] h1, [data-component-theme="portalThemeColor7"] h2, [data-component-theme="portalThemeColor7"] h3, [data-component-theme="portalThemeColor7"] h4, [data-component-theme="portalThemeColor7"] h5, [data-component-theme="portalThemeColor7"] h6, [data-component-theme="portalThemeColor7"] p, [data-component-theme="portalThemeColor7"] p.smallText {
    color: var(--portalThemeOnColor7); }

[data-component-theme="portalThemeColor8"] {
  background-color: var(--portalThemeColor8);
  color: var(--portalThemeOnColor8); }
  [data-component-theme="portalThemeColor8"] h1, [data-component-theme="portalThemeColor8"] h2, [data-component-theme="portalThemeColor8"] h3, [data-component-theme="portalThemeColor8"] h4, [data-component-theme="portalThemeColor8"] h5, [data-component-theme="portalThemeColor8"] h6, [data-component-theme="portalThemeColor8"] p, [data-component-theme="portalThemeColor8"] p.smallText {
    color: var(--portalThemeOnColor8); }

[data-component-theme="portalThemeColor9"] {
  background-color: var(--portalThemeColor9);
  color: var(--portalThemeOnColor9); }
  [data-component-theme="portalThemeColor9"] h1, [data-component-theme="portalThemeColor9"] h2, [data-component-theme="portalThemeColor9"] h3, [data-component-theme="portalThemeColor9"] h4, [data-component-theme="portalThemeColor9"] h5, [data-component-theme="portalThemeColor9"] h6, [data-component-theme="portalThemeColor9"] p, [data-component-theme="portalThemeColor9"] p.smallText {
    color: var(--portalThemeOnColor9); }

[data-component-theme="portalThemeColor10"] {
  background-color: var(--portalThemeColor10);
  color: var(--portalThemeOnColor10); }
  [data-component-theme="portalThemeColor10"] h1, [data-component-theme="portalThemeColor10"] h2, [data-component-theme="portalThemeColor10"] h3, [data-component-theme="portalThemeColor10"] h4, [data-component-theme="portalThemeColor10"] h5, [data-component-theme="portalThemeColor10"] h6, [data-component-theme="portalThemeColor10"] p, [data-component-theme="portalThemeColor10"] p.smallText {
    color: var(--portalThemeOnColor10); }

[data-component-theme="portalThemeColor11"] {
  background-color: var(--portalThemeColor11);
  color: var(--portalThemeOnColor11); }
  [data-component-theme="portalThemeColor11"] h1, [data-component-theme="portalThemeColor11"] h2, [data-component-theme="portalThemeColor11"] h3, [data-component-theme="portalThemeColor11"] h4, [data-component-theme="portalThemeColor11"] h5, [data-component-theme="portalThemeColor11"] h6, [data-component-theme="portalThemeColor11"] p, [data-component-theme="portalThemeColor11"] p.smallText {
    color: var(--portalThemeOnColor11); }

[data-component-theme="portalThemeColor12"] {
  background-color: var(--portalThemeColor12);
  color: var(--portalThemeOnColor12); }
  [data-component-theme="portalThemeColor12"] h1, [data-component-theme="portalThemeColor12"] h2, [data-component-theme="portalThemeColor12"] h3, [data-component-theme="portalThemeColor12"] h4, [data-component-theme="portalThemeColor12"] h5, [data-component-theme="portalThemeColor12"] h6, [data-component-theme="portalThemeColor12"] p, [data-component-theme="portalThemeColor12"] p.smallText {
    color: var(--portalThemeOnColor12); }

body {
  color: #000000;
  background-color: #FFFFFF; }

legend {
  color: #000000; }

footer {
  color: #2A2C2E;
  font-family: Arial;
  font-weight: 400;
  font-size: 16px; }
  footer .footer-bottom {
    background-color: #88ABA2; }

.columnBlockLayout {
  margin-top: 60px;
  margin-bottom: 60px;
  padding: 16px; }
  
.ppFlexContainer {
  display: flex;
  flex-wrap: wrap;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: auto;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px; }

.sectionBlockLayout .columnBlockLayout:has(.ppFlexContainer) {
  display: block !important;
  height: fit-content !important; }

.sectionBlockLayout .container-fluid {
  padding-left: 0;
  padding-right: 0; }

.ppFlexContainer .button1, .ppFlexContainer .button2 {
  white-space: nowrap; }

@media (min-width: 768px) {
  .ppFlexContainer {
    flex-basis: 0%; } }

.breadcrumb {
  background-color: transparent; }
  .breadcrumb > li a {
    color: #2A2C2E; }
    .breadcrumb > li a:hover {
      color: #2A2C2E; }
  .breadcrumb > .active {
    color: #000000; }

.nav-tabs > li > a:hover {
  background-color: #f2f2f2;
  color: #2A2C2E; }

.nav-tabs > li > a:focus {
  background-color: #f2f2f2;
  color: #2A2C2E; }

.btn-primary {
  color: #2A2C2E;
  background-color: #FEA002;
  border-color: #FEA002;
  border-radius: 2px;
  font-family: Arial;
  font-weight: 600;
  font-size: 14px;
  padding: 6px 20px;
  width: fit-content;
  border-style: solid;
  background-color: #FEA002;
  color: #2A2C2E; }
  .btn-primary:hover {
    color: #2A2C2E;
    background-color: #E58700;
    border-color: #E58700; }
  .btn-primary:active, .btn-primary.active {
    color: #2A2C2E;
    background-color: #E58700;
    border-color: #E58700; }
    .btn-primary:active:hover, .btn-primary:active:focus, .btn-primary:active.focus, .btn-primary.active:hover, .btn-primary.active:focus, .btn-primary.active.focus {
      color: #2A2C2E;
      background-color: #E58700;
      border-color: #E58700; }
  .btn-primary:focus, .btn-primary.focus {
    color: #2A2C2E;
    background-color: #E58700;
    border-color: #E58700; }

.static-top.navbar-dark {
  background-color: #2E456B; }

.navbar-toggler {
  background-color: #FEA002; }

.static-top.navbar .menu-bar > .navbar-nav > li > a {
  font-family: Arial;
  font-weight: 400;
  font-size: 20px; }
  .static-top.navbar .menu-bar > .navbar-nav > li > a:hover {
    text-decoration: Underline; }

.navbar-dark .navbar-toggler {
  border: 1px solid #FEA002; }
  .navbar-dark .navbar-toggler:hover {
    background-color: #E58700;
    border-color: #E58700; }
  .navbar-dark .navbar-toggler:focus {
    border: 1px solid #ffffff;
    background-color: #E58700; }
  .navbar-dark .navbar-toggler .navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8, %3Csvg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='%232A2C2E'  stroke-width='3' stroke-linecap='round' stroke-miterlimit='10' d='M4 8h24M4 16h24M4 24h24'/%3E%3C/svg%3E"); }

.navbar-dark .navbar-nav > li > a, .navbar-dark .navbar-nav > li.dropdown > a {
  color: #FFFFFF;
  font-family: Arial;
  font-weight: 400;
  font-size: 20px; }
  .navbar-dark .navbar-nav > li > a:hover, .navbar-dark .navbar-nav > li.dropdown > a:hover {
    background-color: #FEA002;
    color: #000000;
    text-decoration: Underline; }
  .navbar-dark .navbar-nav > li > a:focus, .navbar-dark .navbar-nav > li.dropdown > a:focus {
    background-color: #FEA002;
    color: #000000;
    text-decoration: Underline; }
  .navbar-dark .navbar-nav > li > a:focus-visible, .navbar-dark .navbar-nav > li.dropdown > a:focus-visible {
    background-color: #FEA002;
    color: #000000;
    text-decoration: Underline; }

.navbar-dark .navbar-nav a.show, .navbar-dark .navbar-nav a.dropdown-toggle.show {
  color: #000000;
  background-color: #FEA002;
  text-decoration: Underline; }
  .navbar-dark .navbar-nav a.show:hover, .navbar-dark .navbar-nav a.dropdown-toggle.show:hover {
    color: #000000;
    background-color: #FEA002;
    text-decoration: Underline; }
  .navbar-dark .navbar-nav a.show:focus, .navbar-dark .navbar-nav a.dropdown-toggle.show:focus {
    color: #000000;
    background-color: #FEA002;
    text-decoration: Underline; }

.navbar-dark .navbar-nav .show.dropdown-menu {
  background-color: #FEA002;
  text-decoration: none; }
  .navbar-dark .navbar-nav .show.dropdown-menu > li > a {
    color: #000000;
    text-decoration: none; }
    .navbar-dark .navbar-nav .show.dropdown-menu > li > a:hover {
      background-color: #000000;
      color: #FEA002;
      text-decoration: Underline; }
    .navbar-dark .navbar-nav .show.dropdown-menu > li > a:focus {
      background-color: #000000;
      color: #FEA002;
      text-decoration: Underline; }
  .navbar-dark .navbar-nav .show.dropdown-menu > .active > a {
    background-color: #000000;
    color: #FEA002;
    text-decoration: Underline; }
    .navbar-dark .navbar-nav .show.dropdown-menu > .active > a:hover {
      background-color: #000000;
      color: #FEA002;
      text-decoration: Underline; }
    .navbar-dark .navbar-nav .show.dropdown-menu > .active > a:focus {
      background-color: #000000;
      color: #FEA002;
      text-decoration: Underline; }

.nav > li > a {
  text-decoration: none; }
  .nav > li > a:hover {
    background-color: #f2f2f2; }
  .nav > li > a:focus {
    background-color: #f2f2f2; }

.nav .show.dropdown-menu {
  background-color: #FFFFFF; }
  .nav .show.dropdown-menu > li > a {
    text-decoration: none;
    color: #000000; }
    .nav .show.dropdown-menu > li > a:hover {
      background-color: #f2f2f2;
      color: #000000; }
    .nav .show.dropdown-menu > li > a:focus {
      background-color: #f2f2f2;
      color: #000000; }
  .nav .show.dropdown-menu > .active > a {
    background-color: #f2f2f2;
    color: #000000; }
    .nav .show.dropdown-menu > .active > a:hover {
      background-color: #f2f2f2;
      color: #000000; }
    .nav .show.dropdown-menu > .active > a:focus {
      background-color: #f2f2f2;
      color: #000000; }

.nav > .dropdown > a.show {
  text-decoration: none;
  background-color: Underline; }
  .nav > .dropdown > a.show:hover {
    background-color: Underline; }
  .nav > .dropdown > a.show:focus {
    background-color: Underline; }

@media (min-width: 576px) {
  .row.sectionBlockLayout > .container, .row.sectionBlockLayout > .container-sm {
    max-width: 540px; } }

@media (min-width: 768px) {
  .row.sectionBlockLayout > .container, .row.sectionBlockLayout > .container-md, .row.sectionBlockLayout > .container-sm {
    max-width: 720px; } }

@media (min-width: 992px) {
  .row.sectionBlockLayout > .container, .row.sectionBlockLayout > .container-lg, .row.sectionBlockLayout > .container-md, .row.sectionBlockLayout > .container-sm {
    max-width: 960px; } }

@media (min-width: 1200px) {
  .row.sectionBlockLayout > .container, .row.sectionBlockLayout > .container-lg, .row.sectionBlockLayout > .container-md, .row.sectionBlockLayout > .container-sm, .row.sectionBlockLayout > .container-xl {
    max-width: 1140px; } }

@media (min-width: 1400px) {
  .row.sectionBlockLayout > .container, .row.sectionBlockLayout > .container-lg, .row.sectionBlockLayout > .container-md, .row.sectionBlockLayout > .container-sm, .row.sectionBlockLayout > .container-xl, .row.sectionBlockLayout > .container-xxl {
    max-width: 1320px; } }

.sectionPrimaryColor {
  background-color: #FFCA4B;
  color: #000000; }
  .sectionPrimaryColor h1, .sectionPrimaryColor h2, .sectionPrimaryColor h3, .sectionPrimaryColor h4, .sectionPrimaryColor h5, .sectionPrimaryColor h6, .sectionPrimaryColor p, .sectionPrimaryColor a {
    color: #000000; }
  .sectionPrimaryColor .crmEntityFormView h1, .sectionPrimaryColor .crmEntityFormView h2, .sectionPrimaryColor .crmEntityFormView h3, .sectionPrimaryColor .crmEntityFormView h4, .sectionPrimaryColor .crmEntityFormView h5, .sectionPrimaryColor .crmEntityFormView h6, .sectionPrimaryColor .crmEntityFormView p, .sectionPrimaryColor .crmEntityFormView a {
    color: #000000; }
  .sectionPrimaryColor .entitylist h1, .sectionPrimaryColor .entitylist h2, .sectionPrimaryColor .entitylist h3, .sectionPrimaryColor .entitylist h4, .sectionPrimaryColor .entitylist h5, .sectionPrimaryColor .entitylist h6, .sectionPrimaryColor .entitylist p, .sectionPrimaryColor .entitylist a {
    color: #000000; }

a:not(.btn) {
  text-decoration: underline; }
  a:not(.btn):hover {
    text-decoration: underline; }

a,
.a {
  color: #2A2C2E;
  font-family: Arial;
  font-weight: 400;
  font-size: 14px; }
  a:hover, a:focus,
.a:hover,
.a:focus {
    color: #2A2C2E; }

a[disabled] {
  color: #A19F9D;
  pointer-events: none; }

button[disabled] {
  background-color: #F3F2F1;
  color: #A19F9D;
  pointer-events: none;
  border: 2px; }

.btn-secondary.dropdown-toggle.show {
  color: #2A2C2E;
  background-color: #e6e6e6;
  border-color: #FEA002; }

.btn-default.dropdown-toggle.show {
  color: #2A2C2E;
  background-color: #e6e6e6;
  border-color: #FEA002; }

.btn-primary.dropdown-toggle.show {
  color: #ffffff;
  background-color: #FEA002;
  border-color: #FEA002; }
  .btn-primary.dropdown-toggle.show:hover, .btn-primary.dropdown-toggle.show:focus, .btn-primary.dropdown-toggle.show.focus {
    color: #2A2C2E;
    background-color: #E58700;
    border-color: #b26900; }

.crmEntityFormView, .entitylist, .modal-content, .popover-content {
  background-color: #FFFFFF;
  color: #000000;
  border: 1px solid #f2f2f2; }

.card-body, .list-group-item {
  background-color: #FFFFFF;
  color: #000000; }

.card-header {
  background-color: #f2f2f2;
  color: #000000; }

.facet-list-group-item-title {
  color: #000000; }

.form-control {
  background-color: #FFFFFF !important;
  color: #000000; }

input {
  background-color: #FFFFFF; }

.bootstrap-datetimepicker-widget {
  background-color: #FFFFFF; }
  .bootstrap-datetimepicker-widget table thead tr:first-child th:hover, .bootstrap-datetimepicker-widget table td.day:hover, .bootstrap-datetimepicker-widget table td span:hover {
    color: black; }
  .bootstrap-datetimepicker-widget table td.active:hover, .bootstrap-datetimepicker-widget table td span.active:hover {
    color: white; }

.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: unset; }

.login-heading-section {
  color: unset; }

.static-top.navbar-dark .navbar-brand a {
  color: #ffffff; }

.search-results .highlight {
  color: #000000; }

.table > thead > tr.info > td {
  background-color: #f2f2f2; }

.table > thead > tr.info > th {
  background-color: #f2f2f2; }

.table > thead > tr > td.info {
  background-color: #f2f2f2; }

.table > thead > tr > th.info {
  background-color: #f2f2f2; }

.table > tbody > tr.info > td {
  background-color: #f2f2f2; }

.table > tbody > tr.info > th {
  background-color: #f2f2f2; }

.table > tbody > tr > td.info {
  background-color: #f2f2f2; }

.table > tbody > tr > th.info {
  background-color: #f2f2f2; }

.table > tfoot > tr.info > td {
  background-color: #f2f2f2; }

.table > tfoot > tr.info > th {
  background-color: #f2f2f2; }

.table > tfoot > tr > td.info {
  background-color: #f2f2f2; }

.table > tfoot > tr > th.info {
  background-color: #f2f2f2; }

.table-hover > tbody > tr:hover {
  background-color: #f2f2f2; }

.table-hover > tbody > tr > td.info:hover {
  background-color: #f2f2f2; }

.table-hover > tbody > tr > th.info:hover {
  background-color: #f2f2f2; }

.table-hover > tbody > tr.info:hover > td {
  background-color: #f2f2f2; }

.table-hover > tbody > tr.info:hover > th {
  background-color: #f2f2f2; }

.table-hover > tbody > tr:hover > .info {
  background-color: #f2f2f2; }

.form-close {
  color: #000000; }
  .form-close:hover {
    color: #000000 !important; }
  .form-close:focus {
    color: #000000 !important; }

::placeholder {
  color: #000000 !important; }

.help-block {
  color: #000000; }

.msos-selection-container, .msos-container, .msos-caret-container, .msos-action-buttons:hover {
  color: #000000 !important;
  background: #FFFFFF !important; }

.msos-option-selected, .msos-selected-display-item, .msos-quick-delete, .msos-quick-delete:hover {
  background: #f2f2f2 !important; }

.msos-label, .msos-quick-delete, .msos-quick-delete:hover {
  color: #000000 !important; }

.msos-option-focused:after, .msos-selecteditems:focus .msos-selected-display-item.msos-selecteditem-active:after, .msos-selecteditems-toggle:focus {
  border-color: #000000 !important; }

.msos-option:hover, .msos-option-selected:hover {
  background: #f9f9f9 !important; }

.msos-selecteditems-toggle {
  color: #FFCA4B !important; }

h1,
.h1,
.section-landing-heading,
.section-landing .row > div .section-landing-heading,
.section-inline-search .row > div h1,
.section-landing h1,
.section-search .header-search h1,
.page_section h1,
.color-inverse h1,
h1 p {
  font-family: Arial;
  font-weight: 400;
  font-size: 40px;
  color: #2A2C2E;
  margin-left: 0px;
  margin-right: 0px; }

h2,
.h2,
.section-landing .row > div .section-landing-sub-heading,
.page_section h2,
.color-inverse h2,
h2 p {
  font-family: Arial;
  font-weight: 400;
  font-size: 36px;
  color: #2A2C2E; }

h3,
.h3,
.page_section h3,
.color-inverse h3,
.sidebar-home h3 {
  font-family: Arial;
  font-weight: 400;
  font-size: 24px;
  color: #2A2C2E; }

h4,
.h4,
.poll .poll-header h4,
.poll .poll-tags h4 {
  font-family: Arial;
  font-weight: 700;
  font-size: 24px;
  color: #2A2C2E; }

h5,
.h5 {
  font-family: Arial;
  font-weight: 700;
  font-size: 14px;
  color: #2A2C2E; }

p,
.p,
.page_section p,
.section-diagonal-left p,
.section-diagonal-right p {
  font-family: Arial;
  font-weight: 400;
  font-size: 20px;
  color: #2A2C2E; }

.p2 {
  font-family: Arial;
  font-weight: 400;
  font-size: 16px;
  color: #2A2C2E; }

.button1 {
  color: #2A2C2E;
  border-radius: 2px;
  font-family: Arial;
  font-weight: 600;
  font-size: 14px;
  border-color: #FEA002;
  padding: 6px 20px;
  width: fit-content;
  border-style: solid;
  border-width: 1.6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #FEA002; }

.button1:hover {
  color: #2A2C2E;
  background-color: #E58700;
  border-color: #E58700; }

.button1:active, .button1.active {
  color: #2A2C2E !important;
  background-color: #E58700 !important;
  border-color: #E58700 !important; }

.button1:focus, .button1.focus, .button1:focus-visible {
  color: #2A2C2E;
  background-color: #E58700;
  border-color: #E58700; }

.button2, .btn-secondary, .btn-default {
  color: #FEA002;
  border-radius: 2px;
  font-family: Arial;
  font-weight: 600;
  font-size: 14px;
  border-color: #FEA002;
  padding: 6px 20px;
  width: fit-content;
  border-style: solid;
  border-width: 1.6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: transparent; }

.button2:hover, .btn-secondary:hover, .btn-default:hover {
  color: #2A2C2E;
  background-color: #FEA002;
  border-color: #FEA002; }

.button2:active, .button2.active, .btn-secondary:active, .btn-secondary.active, .btn-default:active, .btn-default.active {
  color: #2A2C2E !important;
  background-color: #FEA002 !important;
  border-color: #FEA002 !important; }

.button2:focus, .button2.focus, .button2:focus-visible, .btn-secondary:focus, .btn-secondary.focus, .btn-secondary:focus-visible, .btn-default:focus, .btn-default.focus, .btn-default:focus-visible {
  color: #2A2C2E;
  background-color: #FEA002;
  border-color: #FEA002; }
  

.static-top.navbar-dark .navbar-brand a p,
.static-top.navbar-dark .navbar-brand a h1,
.static-top.navbar-dark .navbar-brand a h2,
.static-top.navbar-dark .navbar-brand a h3,
.static-top.navbar-dark .navbar-brand a h4,
.static-top.navbar-dark .navbar-brand a h5,
.static-top.navbar-dark .navbar-brand a span {
  display: inline;
  vertical-align: middle;
  color: #FFFFFF; }

h1.siteTitle,
.static-top.navbar-dark .navbar-brand a h1.siteTitle,
.static-top.navbar-dark .navbar-brand a h1.siteTitle > span {
  display: inline;
  vertical-align: middle;
  color: #FFFFFF;
  font-family: Arial;
  font-weight: 400;
  font-size: 30px; }

p.smallText {
  display: inline;
  vertical-align: middle;
  color: #2A2C2E;
  font-family: Arial;
  font-weight: 400;
  font-size: 16px; }

a.anchor-inherit-styles {
  font-size: inherit;
  font-family: inherit;
  color: inherit; }

a.anchor-hover-color-color1:hover {
  color: var(--portalThemeColor1) !important; }

a.anchor-hover-color-color2:hover {
  color: var(--portalThemeColor2) !important; }

a.anchor-hover-color-color3:hover {
  color: var(--portalThemeColor3) !important; }

a.anchor-hover-color-color4:hover {
  color: var(--portalThemeColor4) !important; }

a.anchor-hover-color-color5:hover {
  color: var(--portalThemeColor5) !important; }

a.anchor-hover-color-color6:hover {
  color: var(--portalThemeColor6) !important; }

a.anchor-hover-color-color7:hover {
  color: var(--portalThemeColor7) !important; }

a.anchor-hover-color-color8:hover {
  color: var(--portalThemeColor8) !important; }

a.anchor-hover-color-color9:hover {
  color: var(--portalThemeColor9) !important; }

a.anchor-hover-color-color10:hover {
  color: var(--portalThemeColor10) !important; }

a.anchor-hover-color-color11:hover {
  color: var(--portalThemeColor11) !important; }

a.anchor-hover-color-color12:hover {
  color: var(--portalThemeColor12) !important; }

a.anchor-text-decoration-none {
  text-decoration: none; }

a.anchor-text-decoration-underline {
  text-decoration: underline; }

a.anchor-hover-text-decoration-none:hover {
  text-decoration: none; }

a.anchor-hover-text-decoration-underline:hover {
  text-decoration: underline; }

/*Bootstrap V5 Changes */
.dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {
  text-decoration: none;
  display: block;
  width: auto;
  background-color: #FEA002;
  color: #000000; }

.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {
  text-decoration: none;
  outline: 0;
  width: auto;
  background-color: #FEA002;
  color: #000000; }

.dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857;
  color: #000000;
  white-space: nowrap;
  width: auto; }

h1, .h1, h2, .h2, h3, .h3 {
  margin-top: 21px;
  margin-bottom: 10.5px; }

h4, .h4, h5, .h5, h6, .h6 {
  margin-top: 10.5px;
  margin-bottom: 10.5px; }

.form-select, .crmentityformview select {
  background-repeat: no-repeat;
  background-position: right .75rem center;
  background-size: 16px 12px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23000000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
}

/* till here */
