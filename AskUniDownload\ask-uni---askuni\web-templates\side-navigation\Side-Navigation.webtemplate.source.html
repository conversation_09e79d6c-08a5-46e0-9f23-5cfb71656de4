{% assign depth_offset = depth_offset | default: 0 %}
{% assign current_page = current_page | default: page %}
{% assign current_depth = 0 %}

{% if current_page.children.size > 0 %}
  {% assign leaf_page = false %}
{% else %}
  {% assign leaf_page = true %}
{% endif %}

{% capture page_item %}
  <li class="active pr-color">
    <a href="{{ current_page.url | h }}" title="{{ current_page.title | h }}">
      {% if leaf_page %}
        <span class="fa fa-fw" aria-hidden="true"></span>
      {% else %}
        <span class="fa fa-fw" aria-hidden="true"></span>
      {% endif %}
      {{ current_page.title | h }}
    </a>
    {% unless leaf_page %}
      <ul class="pr-color">
        {% for child in current_page.children %}
        {% if child.entity.logical_name == 'adx_webpage' %}
          <li>
            <a href="{{ child.url | h }}" title="{{ child.title | h }}">
              {% if child.children.size > 0 %}
                <span class="fa fa-fw" aria-hidden="true"></span>
              {% else %}
                <span class="fa fa-fw" aria-hidden="true"></span>
              {% endif %}
              {{ child.title | h }}
            </a>
          </li>
          {% endif %}
        {% endfor %}
      </ul>
    {% endunless %}
  </li>
{% endcapture %}

<ul class="tree pr-color" role="navigation">
  {% assign crumb_count = 0 %}
  {% assign leaf_mode = false %}
  
  {% for crumb in current_page.breadcrumbs %}
    {% unless current_depth < depth_offset %}
      {% if forloop.last and leaf_page %}
        {% assign leaf_mode = true %}
      {% else %}
        <li>
          <a href="{{ crumb.url | h }}" title="{{ crumb.title | h }}">
            <span class="fa fa-fw" aria-hidden="true"></span>
            {{ crumb.title | h }}
          </a>
        </li>
      {% endif %}
      {% assign crumb_count = crumb_count | plus: 1 %}
    {% endunless %}
    {% assign current_depth = current_depth | plus: 1 %}
  {% endfor %}
  
  {% if crumb_count < 1 %}
    {{ page_item }}
  {% elsif crumb_count < 2 and leaf_mode %}
    {% for parent_sibling in current_page.parent.parent.children %}
      {% if parent_sibling.url == current_page.parent.url %}
        <li>
          <a href="{{ current_page.parent.url | h }}" title="{{ current_page.parent.title | h }}">
            <span class="fa fa-fw" aria-hidden="true"></span>
            {{ current_page.parent.title | h }}
          </a>
          <ul>
            {% for sibling in current_page.parent.children %}
            {% if sibling.entity.logical_name == 'adx_webpage' %}
              <li {% if sibling.url == current_page.url %}class="active"{% endif %}>
                <a href="{{ sibling.url | h }}" title="{{ sibling.title | h }}">
                  {% if sibling.children.size > 0 %}
                    <span class="fa fa-fw" aria-hidden="true"></span>
                  {% else %}
                    <span class="fa fa-fw" aria-hidden="true"></span>
                  {% endif %}
                  {{ sibling.title | h }}
                </a>
              </li>
              {% endif %}
            {% endfor %}
          </ul>
        </li>
      {% else %}
       {% if parent_sibling.entity.logical_name == 'adx_webpage' %}
        <li>
          <a href="{{ parent_sibling.url | h }}" title="{{ parent_sibling.title | h }}">
            {% if parent_sibling.children.size > 0 %}
              <span class="fa fa-fw" aria-hidden="true"></span>
            {% else %}
              <span class="fa fa-fw" aria-hidden="true"></span>
            {% endif %}
            {{ parent_sibling.title | h }}
          </a>
        </li>
        {% endif %}
      {% endif %}
    {% endfor %}
  {% else %}
    <li>
      <ul>
        {% if leaf_mode %}
          {% for parent_sibling in current_page.parent.parent.children %}
            {% if parent_sibling.url == current_page.parent.url %}
              <li>
                <a href="{{ current_page.parent.url | h }}" title="{{ current_page.parent.title | h }}">
                  <span class="fa fa-fw" aria-hidden="true"></span>
                  {{ current_page.parent.title | h }}
                </a>
                <ul>
                  {% for sibling in current_page.parent.children %}
                  {% if sibling.entity.logical_name == 'adx_webpage' %}
                    <li {% if sibling.url == current_page.url %}class="active"{% endif %}>
                      <a href="{{ sibling.url | h }}" title="{{ sibling.title | h }}">
                        {% if sibling.children.size > 0 %}
                          <span class="fa fa-fw" aria-hidden="true"></span>
                        {% else %}
                          <span class="fa fa-fw" aria-hidden="true"></span>
                        {% endif %}
                        {{ sibling.title | h }}
                      </a>
                    </li>
                    {% endif %}
                  {% endfor %}
                </ul>
              </li>
            {% else %}
            {% if parent_sibling.entity.logical_name == 'adx_webpage' %}
              <li>
                <a href="{{ parent_sibling.url | h }}" title="{{ parent_sibling.title | h }}">
                  {% if parent_sibling.children.size > 0 %}
                    <span class="fa fa-fw" aria-hidden="true"></span>
                  {% else %}
                    <span class="fa fa-fw" aria-hidden="true"></span>
                  {% endif %}
                  {{ parent_sibling.title | h }}
                </a>
              </li>
              {% endif %}
            {% endif %}
          {% endfor %}
        {% else %}
          {% for sibling in current_page.parent.children %}
            {% if sibling.url == current_page.url %}
              {{ page_item }}
            {% else %}
            {% if sibling.entity.logical_name == 'adx_webpage' %}
              <li>
                <a href="{{ sibling.url | h }}" title="{{ sibling.title | h }}">
                  {% if sibling.children.size > 0 %}
                    <span class="fa fa-fw" aria-hidden="true"></span>
                  {% else %}
                    <span class="fa fa-fw" aria-hidden="true"></span>
                  {% endif %}
                  {{ sibling.title | h }}
                </a>
              </li>
              {% endif %}
            {% endif %}
          {% endfor %}
        {% endif %}
      </ul>
    </li>
  {% endif %}
</ul>