<footer role="contentinfo" class="footer" data-component-theme="portalThemeColor5">
  <div class="footer-bottom d-print-none">
    <div class="container">
      <div class="row">
        <div class="col-lg-9 col-md-9 col-sm-9 text-start">
          {% editable snippets 'Footer' type: 'html' %}
        </div>
        <div id="accessibilityLinkContainer" class="col-lg-3 col-md-3 col-sm-3 text-end">
          
        </div>
      </div>
    </div>
</footer>

<script type="text/javascript">
  window.onload = function() {
    const accessibilityLinkContainer = document.getElementById("accessibilityLinkContainer");
    switch(window.navigator.language) {
      case "fr":
        if (accessibilityLinkContainer != null) {
          const accessibilityText="Accessibilité : partiellement conforme";
          const frenchAccessibilityAnchor = document.createElement("a");
          frenchAccessibilityAnchor.id = "frenchAccesssibleLink";
          frenchAccessibilityAnchor.target = "_blank";
          frenchAccessibilityAnchor.href = "https://go.microsoft.com/fwlink/?linkid=2163806";
          frenchAccessibilityAnchor.title = accessibilityText;
          frenchAccessibilityAnchor.innerText = accessibilityText;
          accessibilityLinkContainer.appendChild(frenchAccessibilityAnchor);
        }
        break;
      case "it":
        if (accessibilityLinkContainer != null) {
          const accessibilityText="Accessibilità: parzialmente conforme";
          const italianAccessibilityAnchor = document.createElement("a");
          italianAccessibilityAnchor.id = "italianAccesssibleLink";
          italianAccessibilityAnchor.target = "_blank";
          italianAccessibilityAnchor.href = "https://go.microsoft.com/fwlink/?linkid=2208177";
          italianAccessibilityAnchor.title = accessibilityText;
          italianAccessibilityAnchor.innerText = accessibilityText;
          accessibilityLinkContainer.appendChild(italianAccessibilityAnchor);
        }
        break;
      default: 
        accessibilityLinkContainer.remove();
    }
  };
 </script>