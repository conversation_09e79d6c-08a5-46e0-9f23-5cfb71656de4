- adx_name: Authentication/Registration/Enabled
  adx_sitesettingid: 04927a3a-6d09-443f-bd58-45fd0e404a5c
  adx_value: true
- adx_description: 'Override query for site search, to apply additional weights and filters. @Query is the query text entered by a user. Lucene query syntax reference: http://lucene.apache.org/core/old_versioned_docs/versions/2_9_1/queryparsersyntax.html'
  adx_name: search/query
  adx_sitesettingid: 07a197a4-67c1-4748-b328-1eca4e644aab
  adx_value: +(@Query) _title:(@Query) _logicalname:knowledgearticle~0.9^0.3 _logicalname:annotation~0.9^0.25 _logicalname:adx_webpage~0.9^0.2 -_logicalname:adx_webfile~0.9 adx_partialurl:(@Query) _logicalname:adx_blogpost~0.9^0.1 -_logicalname:adx_communityforumthread~0.9
- adx_name: Authentication/OpenAuth/Twitter/ConsumerKey
  adx_sitesettingid: 08acec5e-5a30-4fad-995c-e43236631552
- adx_name: Authentication/OpenAuth/LinkedIn/ConsumerSecret
  adx_sitesettingid: 0fde6248-a793-4b9b-82ee-36438b20c68e
- adx_description: A true or false value. If set to true, the local account will be marked as deprecated. The portal user will be required to migrate to a non-deprecated account.
  adx_name: Authentication/Registration/LocalLoginDeprecated
  adx_sitesettingid: 1676a03f-99c8-40d3-8828-3e20ad658b80
  adx_value: False
- adx_description: A date/time value in GMT format to represent the effective date of the current published terms and conditions. If the terms agreement is enabled, portal users that have not accepted the terms after this date will be asked to accept them the next time they sign in. If the date is not provided, and the terms agreement is enabled, the terms will be presented every time portal users sign in.
  adx_name: Authentication/Registration/TermsPublicationDate
  adx_sitesettingid: ********-992d-4ad7-b60e-437e0a838a64
- adx_name: HTTP/X-Frame-Options
  adx_sitesettingid: 1da35e8d-0616-4bc9-b8fc-1a0202c1dd16
  adx_value: SAMEORIGIN
- adx_description: Determines if faceted search is used for this portal.
  adx_name: Search/FacetedView
  adx_sitesettingid: 23b0a67c-cd57-439b-8cef-cd767bc75396
  adx_value: True
- adx_name: Authentication/Registration/ExternalLoginEnabled
  adx_sitesettingid: 290479f9-ba8e-4e40-a0b6-4b817b8ff9d2
  adx_value: true
- adx_name: Authentication/Registration/InvitationEnabled
  adx_sitesettingid: 2de84024-4e4f-46a6-bf50-4ce8c942ce1e
  adx_value: true
- adx_name: Search/IndexNotesAttachments
  adx_sitesettingid: 2e4e4c2f-3e8c-4aa9-8aff-13eb832fa1c6
  adx_value: false
- adx_name: Authentication/OpenIdConnect/AzureAD/Caption
  adx_sitesettingid: 3210a6c3-dd5d-f011-bec2-0022481a70e2
  adx_value: Microsoft Entra ID
- adx_name: Authentication/OpenIdConnect/AzureAD/RebrandDisclaimerEnabled
  adx_sitesettingid: 3410a6c3-dd5d-f011-bec2-0022481a70e2
  adx_value: false
- adx_description: Used to group a set of entities under an entry in the record type facet view.
  adx_name: Search/RecordTypeFacetsEntities
  adx_sitesettingid: 377b8902-8709-4b68-a520-11fd6754db6d
  adx_value: Downloads:annotation,adx_webfile
- adx_name: Profile/ShowMarketingOptionsPanel
  adx_sitesettingid: 37861f4f-1810-46f9-a84f-805d72c1a9c6
  adx_value: True
- adx_name: Authentication/OpenAuth/Twitter/ConsumerSecret
  adx_sitesettingid: 412c7fbb-aef6-4cab-8630-c6c15599f084
- adx_name: Metadata/Template-Version
  adx_sitesettingid: 4b7e664e-d5a5-4968-824e-1b8a7fd50a05
  adx_value: 3.0.2505.1
- adx_description: Site setting that determines if the language code is included in the portal URL.
  adx_name: MultiLanguage/DisplayLanguageCodeInURL
  adx_sitesettingid: 500eb950-f0b0-46c9-a21f-dc6a46ad7734
  adx_value: False
- adx_name: Profile/ForceSignUp
  adx_sitesettingid: 5658a595-d800-4321-a7d4-3866fdd43c11
  adx_value: False
- adx_name: Search/Enabled
  adx_sitesettingid: 5d88d237-a3fb-4572-9cb2-0ff6e454d6a0
  adx_value: true
- adx_name: Authentication/OpenAuth/Microsoft/ClientId
  adx_sitesettingid: 631d09a5-439f-4a4d-a770-0d8f84d1195d
- adx_name: Authentication/Registration/EmailConfirmationEnabled
  adx_sitesettingid: 72eef0d4-fc3b-421f-a5ce-c4c8d41a2f91
  adx_value: true
- adx_name: Authentication/Registration/OpenRegistrationEnabled
  adx_sitesettingid: 7476c090-4c51-4823-9c55-769d3c9d9c3b
  adx_value: true
- adx_description: Set whether the footer web template is output cached.
  adx_name: Footer/OutputCache/Enabled
  adx_sitesettingid: 78f3dcb8-e30d-4586-836f-20e7ad55a4df
  adx_value: True
- adx_description: Site setting that controls the depth of the webpage hierarchy that’s cloned in a newly-added supported language. Web link sets and content snippets are cloned in the newly-added language when webpages are cloned.
  adx_name: MultiLanguage/MaximumDepthToClone
  adx_sitesettingid: 7c5d9702-3889-4b25-bec5-beb91ce7407b
  adx_value: 3
- adx_name: Search/IndexQueryName
  adx_sitesettingid: 7cdf4086-0b68-4b26-8fcc-530750dfbd5a
  adx_value: Portal Search
- adx_name: Authentication/OpenAuth/Facebook/AppSecret
  adx_sitesettingid: 7e7d2923-ed92-4793-bda8-3717a67b111f
- adx_description: Denies use of the portal to minors without parental consent. By default, it is set to false.
  adx_name: Authentication/Registration/DenyMinorsWithoutParentalConsent
  adx_sitesettingid: 8520aa37-d110-4ac1-a60c-80dfe0ea7eb0
  adx_value: false
- adx_name: Authentication/Registration/LocalLoginEnabled
  adx_sitesettingid: 856951f4-14cf-4906-985f-2e3e9fe9f49a
  adx_value: true
- adx_name: Authentication/Registration/LoginButtonAuthenticationType
  adx_sitesettingid: 8966aaba-318f-4f84-86b4-18e188a69fbb
- adx_description: A true or false value. If set to true, the Last Successful Login field on the portal user’s contact will be updated with the date and time when they successfully signed in.
  adx_name: Authentication/LoginTrackingEnabled
  adx_sitesettingid: 8f5dfb72-115f-4a1b-8885-2ac47b4826bd
  adx_value: False
- adx_description: A true or false value. If set to true, the portal will display the terms and conditions of the site. Users must agree to the terms and conditions before they are considered authenticated and can use the site.
  adx_name: Authentication/Registration/TermsAgreementEnabled
  adx_sitesettingid: 911e3723-36a1-4def-afc7-d3a9d368b819
  adx_value: false
- adx_name: Authentication/OpenAuth/Facebook/AppId
  adx_sitesettingid: 9245783d-5b08-4687-87ba-88c3c35c494d
- adx_name: PWAFeature
  adx_sitesettingid: 934ca484-65cf-4a2a-acac-b7bd66e530e3
  adx_value: '{"status":"disable"}'
- adx_description: 'The amount of time the IP address will have to wait if Authentication/LoginThrottling/MaxInvaildAttemptsFromIPAddress occur within Authentication/LoginThrottling/MaxAttemptsTimeLimitTimeSpan amount of time. Default: 00:10:00 (10 mins)'
  adx_name: Authentication/LoginThrottling/IpAddressTimeoutTimeSpan
  adx_sitesettingid: a6ce36be-73be-4f36-b029-c28b155a8985
  adx_value: 00:10:00
- adx_description: 'The amount of time the Authentication/LoginThrottling/MaxInvaildAttemptsFromIPAddress are to be within before the IP address has to wait Authentication/LoginThrottling/IpAddressTimeoutTimeSpan. Default: 00:03:00 (3 mins)'
  adx_name: Authentication/LoginThrottling/MaxAttemptsTimeLimitTimeSpan
  adx_sitesettingid: ac2ef699-cd72-412f-aac0-ea4dc13492ab
  adx_value: 00:03:00
- adx_name: Authentication/OpenAuth/LinkedIn/ConsumerKey
  adx_sitesettingid: ae4d6977-2eda-4bef-9976-09e74884701d
- adx_name: OnlineDomains
  adx_sitesettingid: af5a4257-3b42-46ae-99f8-f838e8dcba32
  adx_value: sharepoint.com;microsoftonline.com
- adx_name: Site/BootstrapV5Enabled
  adx_sitesettingid: b67629e3-8202-4066-b464-4f7b82b1f2d4
  adx_value: true
- adx_description: Enables or disables Azure AD as an external identity provider. By default, it is set to true.
  adx_name: Authentication/Registration/AzureADLoginEnabled
  adx_sitesettingid: b8fe5802-47b0-4a14-b11e-da2ba329d77d
  adx_value: true
- adx_description: Sets whether or not the portal can redirect the user to the profile page after successful sign-in. By default, it is set to true.
  adx_name: Authentication/Registration/ProfileRedirectEnabled
  adx_sitesettingid: ba7236b8-09c7-4ec4-9a97-57a390ae42a6
  adx_value: true
- adx_description: Setting controls whether attachments will be displayed on Knowledge articles
  adx_name: KnowledgeManagement/DisplayNotes
  adx_sitesettingid: bb9eab59-f30c-44f4-b11b-566100704b1e
  adx_value: false
- adx_description: Enabling this setting will show all customer activity on the portal timeline.
  adx_name: CustomerSupport/DisplayAllUserActivitiesOnTimeline
  adx_sitesettingid: c8ac3b37-cee6-42f5-8914-543b88ba22a2
  adx_value: false
- adx_description: >-
    A collection of search logical name filter options. Defining a value here will add dropdown filter options to site-wide search.


    This value should be in the form of name/value pairs, with name and value separated by a colon, and pairs separated by a semicolon. For example: "Forums:adx_communityforum,adx_communityforumthread,adx_communityforumpost;Blogs:adx_blog,adx_blogpost,adx_blogpostcomment".
  adx_name: search/filters
  adx_sitesettingid: cb0c5101-3824-411e-bc52-a4ab59fa013b
- adx_description: Denies use of the portal to minors. By default, it is set to false.
  adx_name: Authentication/Registration/DenyMinors
  adx_sitesettingid: cbb196ea-6d72-49c0-95bd-c769f33e325e
  adx_value: false
- adx_description: 'The Prefix entered here will be used to filter Notes Text, allowing you to control notes exposed on the Portal ex: *WEB*'
  adx_name: KnowledgeManagement/NotesFilter
  adx_sitesettingid: cd65a0a5-50d7-4791-a170-757666446cec
  adx_value: '*WEB*'
- adx_name: Authentication/OpenAuth/Microsoft/ClientSecret
  adx_sitesettingid: e0682549-fade-473d-b49c-2aa81d5aa904
- adx_description: Set whether the header web template is output cached.
  adx_name: Header/OutputCache/Enabled
  adx_sitesettingid: f466f292-b07a-45fd-979b-e6972060ac70
  adx_value: True
- adx_name: ThemeFeature
  adx_sitesettingid: f6e0e841-9ae5-4001-9e46-a3e22f0fff30
  adx_value: '{"status":"enable","selectedThemeId":"329a43fa-5471-4678-8330-d3a0b404e9bb","version":"V2"}'
- adx_description: 'The default number of unauthenticated login attempts from an IP address before the IP address is blocked for Authentication/LoginThrottling/IpAddressTimeoutTimeSpan if the attempts occur within Authentication/LoginThrottling/MaxAttemptsTimeLimitTimeSpan amount of time. Default: 1000'
  adx_name: Authentication/LoginThrottling/MaxInvaildAttemptsFromIPAddress
  adx_sitesettingid: feaef6c9-bde7-4f41-b6cb-b93a6b5cf811
  adx_value: 1000
