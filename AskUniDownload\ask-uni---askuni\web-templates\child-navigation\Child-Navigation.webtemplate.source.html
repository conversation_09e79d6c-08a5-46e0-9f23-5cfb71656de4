{% assign showchildren = showchildren | default: true %}
{% assign showdescriptions = showdescriptions | default: true %}
{% if showchildren and page.children.size > 0 %}
<div class="child-navigation content-panel card     ">
    <div class="card-header">
        <h3>{% editable snippets "Page Children Heading" default: 'In This Section' %}</h3>
    </div>
    {% if showdescriptions %}
    <ul class="list-group">
        {% for node in page.children %}
        <li class="list-group-item">
            <h4 class="list-group-item-heading">
                <a href="{{ node.url | escape }}" aria-label = "Go to {{node.title | escape}} page">{{ node.title | escape }}</a>
            </h4>
            <div class="list-group-item-text">
                {{ node.description }}
            </div>
        </li>
        {% endfor %}
    </ul>
    {% else %}
    <div class="list-group">
        {% for node in page.children %}
        <a href="{{ node.url | escape }}" class=" list-group-item-action list-group-item">{{ node.title | escape }}</a>
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endif %}