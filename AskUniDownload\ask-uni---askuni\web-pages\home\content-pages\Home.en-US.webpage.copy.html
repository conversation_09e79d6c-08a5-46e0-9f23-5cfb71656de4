<div class="row sectionBlockLayout" data-component-theme="portalThemeColor1" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap; flex-direction: row-reverse;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><img src="/Geometric-4.png" alt="" name="Geometric-4.png" style="width: 117%; height: 305.545px; max-width: 100%;"></div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2>Create an engaging headline, welcome, or call to action</h2>
            <button onclick="window.location.href='/'" type="button" value="/" class="button1">Add a call to action here</button>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor3" style="display: flex; flex-wrap: wrap; min-height: 28px;"></div>
<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2 style="text-align: center;">Introduction section for Tiwiex</h2>
            <p style="text-align: center;">Create a short paragraph that shows your target audience a clear benefit to them if they continue past this point and offer direction about the next steps</p>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-4 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><img src="/Circle-1.png" alt="" name="Circle-1.png" style="width: 108px; height: 108px; max-width: 100%; margin-left: auto; margin-right: auto;">
            <h3 style="text-align: center;">Featured Item 1</h3>
            <p style="text-align: center;">Create a short description or engaging message to motivate your audience to find out more about this item.</p>
        </div>
        <div class="col-lg-4 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><img src="/Circle-2.png" alt="" name="Circle-2.png" style="width: 108px; height: 108px; max-width: 100%; margin-left: auto; margin-right: auto;">
            <h3 style="text-align: center;">Featured Item 2</h3>
            <p style="text-align: center;">Create a short description or engaging message to motivate your audience to find out more about this item.</p>
        </div>
        <div class="col-lg-4 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><img src="/Circle-3.png" alt="" name="Circle-3.png" style="width: 108px; height: 108px; max-width: 100%; margin-left: auto; margin-right: auto;">
            <h3 style="text-align: center;">Featured Item 3</h3>
            <p style="text-align: center;">Create a short description or engaging message to motivate your audience to find out more about this item.</p>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor6" style="display: flex; flex-wrap: wrap; min-height: 52px;"></div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor5" style="display: flex; flex-wrap: wrap; height: 15px; min-height: 52px;">
</div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor3" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap; flex-direction: row;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2>Showcase a video</h2>
            <p>Videos are optional, but are often used to summarize the benefits of a product or service. Describe the video content and give viewers a clear reason to click. </p>
            <button onclick="window.location.href='/'" type="button" value="/" class="button1">Add a call to action here</button>
        </div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><video allowfullscreen="allowfullscreen" src="/Video1.mp4" controls="controls" autoplay="" muted="" style="max-width: 100%;"> </video></div>
    </div>
</div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor1" style="display: flex; flex-wrap: wrap; min-height: 52px;"></div>
<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><img src="/Graph-1.png" alt="" name="Graph-1.png" style="width: 100%; height: auto; max-width: 100%;"></div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2>Product or service one</h2>
            <p>Highlight specific benefits of a product or service and invite your site visitors to learn more about how it works and why your organization is the right one to meet their unique needs.</p>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;  flex-direction: row-reverse;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><img src="/Site-mockup-1.png" alt="" name="Site-mockup-1.png" style="width: 100%; height: auto; max-width: 100%;"></div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2>Product or service two</h2>
            <p>Highlight specific benefits of a product or service and invite your site visitors to learn more about how it works and why your organization is the right one to meet their unique needs.</p>
            <div><br></div>
            <p></p>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor3" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><img src="/Geometric-2.png" alt="" name="Geometric-2.png" style="width: 102.56%; height: 343px; max-width: 100%;"></div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2>Share a story</h2>
            <p>Main pages often include links to stories about how individual people or organizations benefit from interacting with your organization.</p>
            <button onclick="window.location.href='/'" type="button" value="/" class="button1">Add a call to action here</button>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor2" style="display: flex; flex-wrap: wrap; min-height: 52px;"></div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor5" style="display: flex; flex-wrap: wrap; min-height: 52px;"></div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor1" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; min-height: 120px;"></div>
            <h2>Introduce another idea</h2>
            <p>This section can be used to offer supporting information or introduce a new idea. It’s best to keep it relevant to the contents of the page so it doesn’t feel out of place. Common topics include a summary of your organization’s mission, people,
                or an upcoming event with links to provide more information. </p>
            <div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; min-height: 120px;"></div>
        </div>
    </div>
</div>