<div class="row sectionBlockLayout" data-component-theme="portalThemeColor3" style="display: flex; flex-wrap: wrap; height: 15px; min-height: 15px;">
</div>
<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2>Pages</h2>
            <p>This section can be used to introduce the contents of the subpages and encourage your target audience to explore those pages. If this content gets too long, consider splitting it into two smaller paragraphs.</p>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><img src="/Circle-1.png" alt="" name="Circle-1.png" style="width: 108px; height: 108px; max-width: 100%; margin-left: auto; margin-right: auto;">
            <h3 style="text-align: center;">Subpage 1</h3>
            <p style="text-align: center;">Create a short description or engaging message to motivate your audience to take action</p>
        </div>
        <div class="col-lg-6 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"><img src="/Circle-2.png" alt="" name="Circle-2.png" style="width: 108px; height: 108px; max-width: 100%; margin-left: auto; margin-right: auto;">
            <h3 style="text-align: center;">Subpage 2</h3>
            <p style="text-align: center;">Create a short description or engaging message to motivate your audience to take action</p>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor1" style="display: flex; flex-wrap: wrap; text-align: left; min-height: auto;">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; min-height: 120px;"></div>
            <h1 style="text-align: center;">Introduce another idea </h1>
            <p style="text-align: center;">This section can be used to offer supporting information or introduce a new idea. It’s best to keep it relevant to the contents of the page so it doesn’t feel out of place. </p>
            <div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap; min-height: 120px;"></div>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor2" style="display: flex; flex-wrap: wrap; min-height: 28px;"></div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor6" style="display: flex; flex-wrap: wrap; min-height: 52px;"></div>