{% assign weblink_set_name = weblink_set_name %}

{% assign showdescriptions = showdescriptions | default: false %}

{% if weblink_set_name %}
  {% assign weblink_set = weblinks[weblink_set_name] %}
  {% if weblink_set %}
    {% if showdescriptions %}
      <div class="{% if weblink_set.editable %}xrm-entity xrm-editable-adx_weblinkset{% endif %}">
        <div class="list-group weblinks">
          {% for link in weblink_set.weblinks %}
            <a class=" list-group-item-action list-group-item weblink" href="{{ link.url | h }}" title="{{ link.tooltip | h }}">
              <h4 class="list-group-item-heading weblink-name">{{ link.name | h }}</h4>
              <div class="list-group-item-text weblink-description">
                {{ link.description | strip_html | text_to_html  }}
              </div>
            </a>
          {% endfor %}
        </div>
        {% editable weblink_set %}
      </div>
    {% else %}
      <div class="card      {% if weblink_set.editable %}xrm-entity xrm-editable-adx_weblinkset{% endif %}">
        <div class="card-header">
          <div class="card-title"><span class="fa fa-bars" aria-hidden="true" style="font-size: 14px;"></span>  {{ snippets["Related Links Navigation Heading"] | default:"Related Links" | h }}</div>
        </div>
        <div class="weblinks list-group">
          {% for link in weblink_set.weblinks %}
            <a class=" list-group-item-action weblink list-group-item" href="{{ link.url | h }}" title="{{ link.tooltip | h }}">
              {% if link.image %}
                {% if link.image.url startswith '.' %}
                  <span class="{{ link.image.url | split:'.' | join | h }}" aria-hidden="true"></span>
                {% else %}
                  <img src="{{ link.image.url | h }}" alt="{{ link.image.alternate_text | h }}" />
                {% endif %}
              {% endif %}
              {{ link.name | h }}
            </a>
          {% endfor %}
        </div>
      {% editable weblink_set %}
      </div>
  {% endif %}
 {% endif %}
{% endif %}