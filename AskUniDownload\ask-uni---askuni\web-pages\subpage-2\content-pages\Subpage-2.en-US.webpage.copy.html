<div class="row sectionBlockLayout" data-component-theme="portalThemeColor6" style="display: flex; flex-wrap: wrap; height: 15px; min-height: 15px;">
</div>
<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap;text-align: left; min-height: auto; ">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;">
            <h2>Subpage 2</h2>
            <p>This page can be customized by adding new sections and components.</p>
        </div>
    </div>
</div>
<div class="row sectionBlockLayout" style="display: flex; flex-wrap: wrap;text-align: left; min-height: auto; ">
    <div class="container" style="padding: 0px; display: flex; flex-wrap: wrap;">
        <div class="col-lg-12 columnBlockLayout" style="flex-grow: 1; display: flex; flex-direction: column; min-width: 300px;"></div>
    </div>
</div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor2" style="display: flex; flex-wrap: wrap; min-height: 28px;"></div>
<div class="row sectionBlockLayout" data-component-theme="portalThemeColor6" style="display: flex; flex-wrap: wrap;  min-height: 52px;"></div>