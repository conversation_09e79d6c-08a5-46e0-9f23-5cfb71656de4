{% assign page_type = page_type %}

<div class="tabs-header" id="mainContent">
  <ul class="nav nav-tabs">
    {% if page_type == 'topic' %}
      <li class="nav-item" role="presentation">
        <a class="nav-link" title="{{ page.parent.title }}" href="{{ page.parent.url }}">{{ page.parent.title }}</a>
      </li>
      {% for child in page.parent.children %}
        {% if child.title == page.title %}
          <li role="presentation" class=" nav-item active">
            <a class="nav-link" title="{{ page.title }}" href="{{ page.url }}">{{ page.title }}</a>
          </li>
        {% else %}
        <li class="nav-item" role="presentation">
          <a class="nav-link" title="{{ child.title }}" href="{{ child.url }}">{{ child.title }}</a>
        </li>
        {% endif %}
      {% endfor %}
    {% else %}
      <li role="presentation" class=" nav-item active">
        <a class="nav-link" title="{{ page.title }}" href="{{ page.url }}">{{ page.title }}</a>
      </li>
      {% for child in page.children %}
        <li class="nav-item" role="presentation">
          <a class="nav-link" title="{{ child.title }}" href="{{ child.url }}">{{ child.title }}</a>
        </li>
      {% endfor %}
    {% endif %}
  </ul>
</div>