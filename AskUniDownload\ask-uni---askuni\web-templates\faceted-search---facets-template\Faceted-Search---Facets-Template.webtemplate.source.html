{%raw%}
 <script id="facets-view-results" type="text/x-handlebars-template">
  {{#each facetViews}}

  {{#ifvalue facetName value="_logicalname"}}
   {{#if facetData}}
    <div class="facet-view short-list card     ">
     <div class="facet-title card-header">{%endraw%}{{ snippets['Search/Facet/RecordType'] | default: resx['Facet_RecordType'] | h }}{%raw%}</div>
     <div class="card-body">
      <ul class="facet-list-group"
       role="listbox" aria-label="{%endraw%}{{ snippets['Search/Facet/RecordType'] | default: resx['Facet_RecordType'] | h }}{%raw%}">
       {{#each facetData}}
        <li class="facet-list-group-item clearfix control-item {{#if active}}active{{/if}}"
         tabIndex="0"
         data-facet="{{../facetName}}" data-control-value="{{name}}"
         role="option" aria-label="{{labelText}}" title="{{labelText}}" aria-selected="{{#if active}}true{{else}}false{{/if}}" aria-checked="{{#if active}}true{{else}}false{{/if}}">
         <span class="facet-list-group-item-count float-end">{{hitCount}}</span>
         <div class="facet-list-group-item-title-container">
          <span class="facet-list-group-item-title float-start">{{displayName}}</span>
         </div>
        </li>
       {{/each}}
       <button type="button" class="show-more btn btn-link btn-xs" tabIndex="0">{%endraw%}{{ snippets['Search/Facet/More'] | default: resx['Facet_Show_More'] | h }}{%raw%}</button>
       <button type="button" class="show-less btn btn-link btn-xs" tabIndex="0">{%endraw%}{{ snippets['Search/Facet/Less'] | default: resx['Facet_Show_Less'] | h }}{%raw%}</button>
      </ul>
     </div>
    </div>
   {{/if}}
  {{/ifvalue}}

  {{#ifvalue facetName value="modifiedon.date"}}
  {{#if noData}}
  {{else}}
   <div class="facet-view card     ">
    <div class="facet-title card-header">{%endraw%}{{ snippets['Search/Facet/ModifiedDate'] | default: resx['Facet_DateModified'] | h }}{%raw%}</div>
    <div class="card-body">
     <ul class="facet-list-group"
      role="listbox" aria-label="{%endraw%}{{ snippets['Search/Facet/ModifiedDate'] | default: resx['Facet_DateModified'] | h }}{%raw%}">
      {{#each facetData}}
       <li class="facet-list-group-item clearfix control-item {{#if active}}active{{/if}}" tabIndex="0" data-facet="{{../facetName}}" data-control-value="{{name}}"
        role="option" aria-label="{{labelText}}" title="{{labelText}}"  aria-checked="{{#if active}}true{{else}}false{{/if}}">
        <span class="facet-list-group-item-count float-end">{{hitCount}}</span>
        <div class="facet-list-group-item-title-container">
         <span class="facet-list-group-item-title float-start">{{displayName}}</span>
        </div>
       </li>
      {{/each}}
     </ul>
    </div>
   </div>
  {{/if}}
  {{/ifvalue}}

  {{#ifvalue facetName value="rating"}}
   {{#if facetData}}
    <div class= "facet-view short-list card     ">
     <div class="facet-title card-header">{%endraw%}{{ snippets['Search/Facet/Rating'] | default: resx['Facet_Rating'] | h }}{%raw%}</div>
     <div class="card-body">
      <ul class="facet-list-group rating-facet-group"
       role="listbox" aria-label="{%endraw%}{{ snippets['Search/Facet/Rating'] | default: resx['Facet_Rating'] | h }}{%raw%}">
       {{#each facetData}}
        {{#if skipStars}}
         <li class="facet-list-group-item clearfix control-item {{#if ../noActive}}active{{/if}}" data-facet="{{../facetName}}" data-control-value="" tabIndex={{#if ../noActive}}0{{else}}-1{{/if}}"
          role="option" aria-label="{%endraw%}{{ snippets['Search/Facet/All'] | default: resx['Facet_All'] | h }}{%raw%} {{hitCount}}" aria-selected="{{#if  ../noActive}}true{{else}}false{{/if}}" aria-checked="{{#if  ../noActive}}true{{else}}false{{/if}}">
          <span class="facet-list-group-item-title float-start">{%endraw%}{{ snippets['Search/Facet/All'] | default: resx['Facet_All'] | h }}{%raw%}</span>
         </li>
        {{else}}
         <li class="facet-list-group-item clearfix control-item {{#if active}}active{{/if}}" data-facet="{{../facetName}}" data-control-value="{{name}}" tabIndex="{{#if active}}0{{else}}-1{{/if}}"
         role="option" aria-label="{{ratingLabel}} {{hitCount}}" aria-selected="{{#if active}}true{{else}}false{{/if}}" aria-checked="{{#if active}}true{{else}}false{{/if}}">
          <span aria-label="{{ratingLabel}}" class="facet-list-group-item-title">
           {{#each filledStars}}
            <span class="rating-star rating-star-filled"></span>
           {{/each}}
           {{#each emptyStars}}
            <span class="rating-star rating-star-empty"></span>
           {{/each}}
           <span aria-hidden="true">{%endraw%}{{resx.Facet_Rating_And_Up}}{%raw%}</span>
          </span>
          <span class="facet-list-group-item-count float-end">{{hitCount}}</span>
         </li>
        {{/if}}
       {{/each}}
      </ul>
     </div>
    </div>
   {{/if}}
  {{/ifvalue}}

  {{#ifvalue facetName value="associated.product"}}
   {{#if facetData}}
    <div class="facet-view facet-view-multiple-select short-list card     ">
     <div class="facet-title card-header">{%endraw%}{{ snippets['Search/Facet/Product'] | default: resx['Facet_Products'] | h }}{%raw%}</div>
     <div class="card-body">
      <ul class="facet-list-group" aria-label="{%endraw%}{{ snippets['Search/Facet/Product'] | default: resx['Facet_Products'] | h }}{%raw%}">
       <li class="facet-list-group-item clearfix control-item {{#if noActive}}active{{/if}}" 
        data-facet="{{../facetName}}" data-control-value="" 
        tabIndex={{#if noActive}}0{{else}}-1{{/if}}"
        aria-label="{%endraw%}{{ snippets['search/facet/all'] | default: resx['Facet_All'] | h }}{%raw%}" 
        aria-selected="{{#if  noActive}}true{{else}}false{{/if}}">
         <span class="facet-list-group-item-title float-start">{%endraw%}{{ snippets['search/facet/all'] | default: resx['Facet_All'] | h }}{%raw%}</span>
       </li>
       {{#each facetData}}
        <li class="facet-list-group-item clearfix control-item {{#if active}}active{{/if}}" 
         data-facet="{{../facetName}}" data-control-value="{{name}}"
         tabIndex="{{#if active}}0{{else}}-1{{/if}}" aria-label="{{displayName}} {{hitCount}}" 
         aria-selected="{{#if active}}true{{else}}false{{/if}}"
         role="checkbox" aria-checked="{{#if active}}true{{else}}false{{/if}}" 
         data-control-display-name="{{displayName}}">
          <span class="facet-list-group-item-count float-end">{{hitCount}}</span>
          <div class="facet-list-group-item-title-container">
           <label class="facet-list-group-item-title float-start">
            <input type="checkbox" {{#if active}}checked{{/if}} tabindex="-1" role="presentation"/>
            {{displayName}}
           </label>
          </div>
        </li>
       {{/each}}
       <button type="button" class="show-more btn btn-link btn-xs" tabIndex="0">{%endraw%}{{ snippets['Search/Facet/More'] | default: resx['Facet_Show_More'] | h }}{%raw%}</button>
       <button type="button" class="show-less btn btn-link btn-xs" tabIndex="0">{%endraw%}{{ snippets['Search/Facet/Less'] | default: resx['Facet_Show_Less'] | h }}{%raw%}</button>
      </ul>
     </div>
    </div>
   {{/if}}
  {{/ifvalue}}

  {{/each}}
 </script>

 {% endraw %}